import * as express from 'express';
import { urlencoded, json } from 'express';
import { createServer, proxy } from 'aws-serverless-express';
import { NestFactory } from '@nestjs/core';
import { eventContext } from 'aws-serverless-express/middleware';
import { ExpressAdapter } from '@nestjs/platform-express';
import { AppModule } from './app.module';

// eslint-disable-next-line @typescript-eslint/no-var-requires
import * as AWSXRay from 'aws-xray-sdk';
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('http'));
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureHTTPsGlobal(require('https'));
AWSXRay.capturePromise();
// eslint-disable-next-line @typescript-eslint/no-var-requires
AWSXRay.captureAWS(require('aws-sdk'));

const binaryMimeTypes: string[] = [];

let cachedServer: express.Express;

async function bootstrap(): Promise<express.Express> {
  const traceId = AWSXRay.getSegment()?.['trace_id'];
  console.log(`X-Ray Trace ID: ${traceId}`);
  if (!cachedServer) {
    const expressApp = express();
    const app = await NestFactory.create(
      AppModule,
      new ExpressAdapter(expressApp),
    );
    app.use(json({ limit: '50mb' }));
    app.use(urlencoded({ extended: true, limit: '50mb' }));

    // Enable CORS and other middlewares
    app.use(eventContext());
    app.enableCors({
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Amz-Date',
        'X-Api-Key',
        'X-Amz-Security-Token',
      ],
      origin: '*',
      methods: ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT'],
    });

    app.getHttpAdapter().getInstance().disable('x-powered-by');

    // Apply X-Ray middleware to capture traces for each incoming request
    // expressApp.use(
    //   AWSXRay.express.openSegment('dev-gusmiddleware-services/dev'),
    // );

    await app.init();

    // Close the X-Ray segment for the current request
    // expressApp.use(AWSXRay.express.closeSegment());

    cachedServer = createServer(expressApp, undefined, binaryMimeTypes);
  }

  return cachedServer;
}

module.exports.handler = async (event, context) => {
  cachedServer = await bootstrap();
  event = JSON.parse(JSON.stringify(event).replaceAll('/student/', '/'));
  return proxy(cachedServer, event, context, 'PROMISE').promise;
};

// import { NestFactory } from '@nestjs/core';
// import { AppModule } from './app.module';

// async function bootstrap() {
//   const app = await NestFactory.create(AppModule);

//   // enableCors
//   app.enableCors({
//     allowedHeaders: [
//       'Content-Type',
//       'Authorization',
//       'X-Amz-Date',
//       'X-Api-Key',
//       'X-Amz-Security-Token',
//     ],
//     origin: '*',
//     methods: ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'PATCH', 'POST', 'PUT'],
//   });
//   await app.listen(3002);
// }

// bootstrap();
