import { Injectable } from '@nestjs/common';
import { DynamoDBService } from 'apps/src/common/dynamodb.service';
import * as AWS from 'aws-sdk';
AWS.config.update({ region: 'eu-west-1' });

// const translate = new AWS.Translate();
@Injectable()
export class OapTranslationService {
  constructor(private readonly dynamoDBService: DynamoDBService) {}

  async translateJson(inputJson, language): Promise<any> {
    const fieldsToTranslate = [
      'description',
      'headerTitle',
      'displayName',
      'layout.collegeInfo.text',
      'progressInfo.displayName',
      'verificationMessage.displayName',
      'verificationMessage.message',
      'markdownText',
    ];

    const translateField = async (obj, fieldPath) => {
      const keys = fieldPath.split('.');
      let target = obj;

      while (keys.length > 1) {
        target = target?.[keys.shift()];
        if (!target) return;
      }

      const key = keys[0];
      if (target?.[key]) {
        target[key] =
          fieldPath === 'description'
            ? await this.getTranslatedText(
                `${inputJson.PK}_${inputJson?.SK}_${key}`,
                language,
                target[key],
              )
            : await this.getTranslatedText(target[key], language);
      }
    };

    await Promise.all(
      fieldsToTranslate.map((field) => translateField(inputJson, field)),
    );

    // Recursive function to translate fieldData (including subsections)
    const translateFieldData = async (fields, subsectionName = null) => {
      return await Promise.all(
        fields.map(async (field) => {
          const fieldKeys = [
            'text',
            'displayName',
            'placeholder',
            'label',
            'rules.required',
            'rules.pattern.message',
            'displaySubInformation',
            'addOnButtonDetails.placeHolder',
            'markdownText',
            'pdfLabel',
          ];

          await Promise.all(
            fieldKeys.map(async (key) => {
              const keys = key.split('.');
              let obj = field;

              while (keys.length > 1) {
                obj = obj?.[keys.shift()];
                if (!obj) return;
              }

              const lastKey = keys[0];
              if (obj?.[lastKey]) {
                if (['text', 'label', 'displaySubInformation'].includes(key)) {
                  obj[lastKey] = subsectionName
                    ? await this.getTranslatedText(
                        `${inputJson.PK}_${inputJson?.SK}_${subsectionName}_${lastKey}_${field?.indexOrder}`,
                        language,
                        obj[lastKey],
                      )
                    : await this.getTranslatedText(
                        `${inputJson.PK}_${inputJson?.SK}_${lastKey}_${field?.indexOrder}`,
                        language,
                        obj[lastKey],
                      );
                } else {
                  obj[lastKey] = await this.getTranslatedText(
                    obj[lastKey],
                    language,
                  );
                }
              }
            }),
          );
          if (
            field.type === 'subsection' &&
            field[field.subSection]?.fieldData
          ) {
            field[field.subSection].fieldData = await translateFieldData(
              field[field.subSection].fieldData,
              field.subSection,
            );
          }

          return field;
        }),
      );
    };

    if (Array.isArray(inputJson.fieldData)) {
      inputJson.fieldData = await translateFieldData(inputJson.fieldData);
    }

    if (Array.isArray(inputJson.section)) {
      inputJson.section = await Promise.all(
        inputJson.section.map(async (sec) => {
          if (sec.displayName) {
            sec.displayName = await this.getTranslatedText(
              sec.displayName,
              language,
            );
          }
          return sec;
        }),
      );
    }
    if (inputJson?.staticContents) {
      const translateObject = async (obj: any): Promise<any> => {
        if (typeof obj === 'string') {
          return await this.getTranslatedText(obj, language);
        }

        if (Array.isArray(obj)) {
          return await Promise.all(obj.map((item) => translateObject(item)));
        }

        if (obj && typeof obj === 'object') {
          const entries = Object.entries(obj);
          const translatedEntries = await Promise.all(
            entries.map(async ([key, value]) => [
              key,
              await translateObject(value),
            ]),
          );
          return Object.fromEntries(translatedEntries);
        }

        return obj;
      };

      inputJson.staticContents = await translateObject(
        inputJson.staticContents,
      );
    }

    return inputJson;
  }

  async getTranslatedText(textKey, language, text = null) {
    const tableName = `gus-oap-localization-${process.env.STAGE}`;
    const details = await this.dynamoDBService.getObject(tableName, {
      PK: language,
      SK: textKey,
    });
    console.log('From Db', details);
    if (details.Item?.text) {
      return details.Item.text;
    }

    return text || textKey

    // const translated = await this.translateText(
    //   text || textKey,
    //   'en',
    //   language,
    // );
    // console.log('Translated', translated);
    // // Only save if the translated text is different from the original
    // if (translated !== (text || textKey)) {
    //   await this.dynamoDBService.putObject(tableName, {
    //     Item: {
    //       PK: language,
    //       SK: textKey,
    //       text: translated,
    //       originalText: text || textKey,
    //       createdAt: new Date().toISOString(),
    //     },
    //   });
    // }

    // return translated;
  }

  // async translateText(text, sourceLang, targetLang) {
  //   try {
  //     const params = {
  //       Text: text,
  //       SourceLanguageCode: sourceLang,
  //       TargetLanguageCode: targetLang,
  //     };
  //     const result = await translate.translateText(params).promise();
  //     return result.TranslatedText;
  //   } catch (error) {
  //     console.error('Error translating text:', error);
  //     return text; // Fallback to original text in case of error
  //   }
  // }
}
